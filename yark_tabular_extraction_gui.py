import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
import sys
from pathlib import Path
import datetime
import numpy as np
import cv2
import easyocr
from docx import Document
from docx.shared import Inches, RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
import re
import tempfile

# Import LaTeX text processor
from latex_text_processor import clean_latex_for_word

# Initialize Enhanced Camelot Integration
CAMELOT_AVAILABLE = False
ENHANCED_CAMELOT_AVAILABLE = False
try:
    from enhanced_camelot_integration import enhanced_camelot
    camelot_status = enhanced_camelot.get_status()
    if camelot_status['available']:
        ENHANCED_CAMELOT_AVAILABLE = True
        print("✅ Enhanced Camelot integration available for advanced table extraction")
        print(f"   📋 Camelot version: {camelot_status.get('camelot_version', 'unknown')}")
    else:
        print("⚠️ Enhanced Camelot not available - PDF table extraction disabled")
        print("   💡 Install with: pip install camelot-py[cv]")
except ImportError:
    print("⚠️ Enhanced Camelot integration not available")
    # Fallback to basic Camelot
    try:
        import camelot
        CAMELOT_AVAILABLE = True
        print("✅ Basic Camelot available for table extraction")
    except ImportError:
        print("⚠️ Camelot not available - using fallback table extraction")
        print("   💡 Install with: pip install camelot-py[cv]")

# Try to import PIL for logo display and image processing
try:
    from PIL import Image, ImageTk, ImageEnhance, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available - logo will not be displayed")

# Try to import LaTeX OCR
try:
    from pix2tex.cli import LatexOCR
    LATEX_OCR_AVAILABLE = True
    print("✅ LaTeX OCR available - will use for mathematical content")
except ImportError:
    LATEX_OCR_AVAILABLE = False
    print("⚠️  LaTeX OCR not available. Install with: pip install pix2tex")

# Initialize Multi-Engine OCR System
print("🚀 Initializing Multi-Engine OCR System...")
try:
    from multi_engine_ocr import multi_ocr
    print("✅ Multi-Engine OCR System initialized successfully")
    MULTI_OCR_AVAILABLE = True

    # Also initialize EasyOCR for backward compatibility
    easyocr_reader = easyocr.Reader(['en'])
    print("✅ EasyOCR fallback initialized")
    OCR_AVAILABLE = True
except Exception as e:
    print(f"❌ Multi-Engine OCR initialization failed: {e}")
    MULTI_OCR_AVAILABLE = False
    # Fallback to basic EasyOCR
    try:
        import easyocr
        easyocr_reader = easyocr.Reader(['en'])
        print("✅ Fallback to basic EasyOCR successful")
        OCR_AVAILABLE = True
    except Exception as e2:
        print(f"❌ Complete OCR initialization failed: {e2}")
        OCR_AVAILABLE = False

# Smart OCR Configuration
SMART_OCR_CONFIG = {
    'large_cell_area_threshold': 0.05,      # Cells >5% of image area use LaTeX OCR
    'large_cell_width_threshold': 0.15,     # Cells >15% of image width use LaTeX OCR
    'medium_cell_area_threshold': 0.02,     # Medium cells >2% area with good height
    'medium_cell_height_threshold': 0.08,   # Medium cells need >8% height
    'confidence_boost_latex': 0.2,          # Boost confidence for LaTeX results
    'crop_padding': 5,                      # Padding around detected regions
}

def preprocess_image_for_table(img):
    """Enhanced image preprocessing for better table OCR"""
    print("  🔧 Applying advanced preprocessing...")

    # Convert to numpy array for OpenCV processing
    img_array = np.array(img)

    # Convert to grayscale if needed
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array

    # Analyze image characteristics
    mean_brightness = np.mean(gray)
    print(f"    📊 Image brightness: {mean_brightness:.1f}/255")

    # Adaptive enhancement based on image characteristics
    if mean_brightness < 100:
        # Dark image - brighten
        gray = cv2.convertScaleAbs(gray, alpha=1.3, beta=20)
    elif mean_brightness > 200:
        # Bright image - enhance contrast
        gray = cv2.convertScaleAbs(gray, alpha=0.9, beta=-10)

    # Advanced contrast enhancement
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)

    # Noise reduction
    denoised = cv2.bilateralFilter(enhanced, 5, 50, 50)

    # Sharpening for text clarity
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(denoised, -1, kernel)

    # Scale up image if it's small (helps with OCR accuracy)
    height, width = sharpened.shape
    if width < 1000 or height < 1000:
        scale_factor = 2
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        sharpened = cv2.resize(sharpened, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        print(f"    📏 Scaled image from {width}x{height} to {new_width}x{new_height}")

    print("    ✅ Advanced preprocessing completed")
    return Image.fromarray(sharpened)

def detect_mathematical_content(text):
    """Detect if text contains mathematical expressions"""
    if not text or len(text.strip()) < 2:
        return False

    # Mathematical indicators
    math_patterns = [
        r'[×÷±∞∑∏∫√∂∆∇]',  # Mathematical symbols
        r'[₀₁₂₃₄₅₆₇₈₉]',      # Subscripts
        r'[⁰¹²³⁴⁵⁶⁷⁸⁹]',      # Superscripts
        r'\^[0-9]',             # Power notation
        r'_[0-9]',              # Subscript notation
        r'[a-zA-Z][₀₁₂₃₄₅₆₇₈₉]', # Variables with subscripts
        r'[a-zA-Z]\^[0-9]',     # Variables with powers
        r'\\[a-zA-Z]+',         # LaTeX commands
        r'\{[^}]*\}',           # Curly braces (LaTeX)
        r'[a-zA-Z]/[a-zA-Z]',   # Fractions like a/b
        r'[0-9]/[0-9]',         # Numeric fractions
        r'[=≠<>≤≥]',            # Comparison operators
        r'[∈∉⊂⊃∪∩]',           # Set theory symbols
        r'[αβγδεζηθικλμνξοπρστυφχψω]', # Greek letters
        r'[ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]', # Greek capitals
        r'[a-zA-Z]\s*[=≠<>≤≥]\s*[a-zA-Z0-9]', # Equations
    ]

    # Check for mathematical patterns
    for pattern in math_patterns:
        if re.search(pattern, text):
            return True

    # Check for multiple variables/coefficients (like "2x + 3y")
    if len(re.findall(r'[a-zA-Z]', text)) >= 2 and any(c.isdigit() for c in text):
        return True

    return False

def determine_ocr_method_by_content(img_crop, bbox, img_width, img_height):
    """Determine OCR method based on content analysis rather than size"""
    # Calculate cell dimensions for logging
    left = min(point[0] for point in bbox)
    top = min(point[1] for point in bbox)
    right = max(point[0] for point in bbox)
    bottom = max(point[1] for point in bbox)

    cell_width = right - left
    cell_height = bottom - top
    area_ratio = (cell_width * cell_height) / (img_width * img_height)

    print(f"    📏 Cell analysis: {cell_width:.0f}x{cell_height:.0f} (area: {area_ratio:.1%})")

    # First, do a quick normal OCR to analyze content
    try:
        img_array = np.array(img_crop)
        quick_results = easyocr_reader.readtext(img_array)

        if quick_results:
            # Get the text with highest confidence
            best_result = max(quick_results, key=lambda x: x[2])
            detected_text = best_result[1].strip()
            confidence = best_result[2]

            print(f"    🔍 Quick scan detected: '{detected_text}' (conf: {confidence:.2f})")

            # Analyze content for mathematical indicators
            if detect_mathematical_content(detected_text):
                print(f"    🧮 Mathematical content detected - using LaTeX OCR")
                return 'latex', detected_text, confidence
            else:
                print(f"    📝 Regular text detected - using normal OCR")
                return 'normal', detected_text, confidence
        else:
            print(f"    ⚠️  No text detected in quick scan - using normal OCR")
            return 'normal', "", 0.0

    except Exception as e:
        print(f"    ⚠️  Quick scan failed: {e} - using normal OCR")
        return 'normal', "", 0.0

def extract_text_with_smart_ocr(img_crop, bbox, img_width, img_height):
    """Extract text using the appropriate OCR method based on content analysis"""
    ocr_method, quick_text, quick_confidence = determine_ocr_method_by_content(img_crop, bbox, img_width, img_height)

    if ocr_method == 'latex' and LATEX_OCR_AVAILABLE:
        try:
            print("    🧮 Attempting LaTeX OCR...")
            model = LatexOCR()
            latex_result = model(img_crop)

            if latex_result and len(latex_result.strip()) > 0:
                # Clean the LaTeX result for better readability
                cleaned_result = clean_latex_for_word(latex_result, 'latex')
                print(f"    ✅ LaTeX OCR successful: {latex_result}")
                print(f"    🧹 Cleaned result: {cleaned_result}")
                return cleaned_result, 'latex'
            else:
                print("    ⚠️  LaTeX OCR returned empty result, falling back to normal OCR")
        except Exception as e:
            print(f"    ⚠️  LaTeX OCR failed: {e}, falling back to normal OCR")

    # Use normal OCR (or quick scan results if available)
    if ocr_method == 'normal' and quick_text and quick_confidence > 0.3:
        # Use the quick scan results if they're good enough
        print(f"    ✅ Using quick scan results: {quick_text}")
        return quick_text, 'normal'
    else:
        # Run full normal OCR
        try:
            print("    📝 Using normal EasyOCR...")
            img_array = np.array(img_crop)
            results = easyocr_reader.readtext(img_array)

            if results:
                best_result = max(results, key=lambda x: x[2])
                text = best_result[1]
                # Clean the normal OCR result for better readability
                cleaned_text = clean_latex_for_word(text, 'normal')
                print(f"    ✅ Normal OCR successful: {text}")
                if cleaned_text != text:
                    print(f"    🧹 Cleaned result: {cleaned_text}")
                return cleaned_text, 'normal'
            else:
                print("    ⚠️  Normal OCR returned no results")
                return "", 'normal'
        except Exception as e:
            print(f"    ❌ Normal OCR failed: {e}")
            return "", 'normal'

def smart_ocr_extraction(img_processed):
    """Perform smart OCR extraction by detecting text regions and applying appropriate OCR method"""
    print("  🧠 Starting smart OCR extraction...")

    # First, use EasyOCR to detect all text regions
    img_array = np.array(img_processed)
    initial_results = easyocr_reader.readtext(img_array)
    print(f"  📍 EasyOCR detected {len(initial_results)} text regions")

    smart_results = []
    img_width, img_height = img_processed.size

    for i, (bbox, text, confidence) in enumerate(initial_results):
        print(f"  🔍 Processing region {i+1}/{len(initial_results)}: '{text}' (conf: {confidence:.2f})")

        # Extract the region for targeted OCR with configurable padding
        padding = SMART_OCR_CONFIG['crop_padding']
        left = max(0, int(min(point[0] for point in bbox)) - padding)
        top = max(0, int(min(point[1] for point in bbox)) - padding)
        right = min(img_width, int(max(point[0] for point in bbox)) + padding)
        bottom = min(img_height, int(max(point[1] for point in bbox)) + padding)

        # Crop the region
        img_crop = img_processed.crop((left, top, right, bottom))

        # Apply smart OCR based on cell size
        enhanced_text, ocr_method = extract_text_with_smart_ocr(img_crop, bbox, img_width, img_height)

        # Use enhanced text if it's better, otherwise keep original
        final_text = enhanced_text if enhanced_text.strip() else text
        final_confidence = confidence

        # Boost confidence for LaTeX OCR results
        if ocr_method == 'latex' and enhanced_text.strip():
            boost = SMART_OCR_CONFIG['confidence_boost_latex']
            final_confidence = min(0.95, confidence + boost)
            print(f"    ⬆️  Boosted confidence for LaTeX result: {final_confidence:.2f} (+{boost})")

        smart_results.append((bbox, final_text, final_confidence))
        print(f"    ✅ Final result: '{final_text}' (method: {ocr_method}, conf: {final_confidence:.2f})")

    print(f"  🎯 Smart OCR completed: {len(smart_results)} enhanced regions")
    return smart_results

def create_word_table(doc, table_data):
    """Create a Word table from table data"""
    if not table_data:
        return

    # Determine table dimensions
    max_cols = max(len(row) for row in table_data)
    rows = len(table_data)

    # Create table
    table = doc.add_table(rows=rows, cols=max_cols)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Fill table
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            if j < len(row.cells):
                cell = row.cells[j]
                cell.text = str(cell_data)

    # Make first row bold (assuming it's header)
    if table.rows:
        for cell in table.rows[0].cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.bold = True

def extract_table_from_results(results):
    """Extract table structure from OCR results"""
    if not results:
        return None

    # Simple table extraction - group by Y position (rows) and X position (columns)
    rows = {}

    for bbox, text, confidence in results:
        if confidence < 0.3 or not text.strip():
            continue

        # Get center Y position for row grouping
        center_y = (min(point[1] for point in bbox) + max(point[1] for point in bbox)) / 2
        center_x = (min(point[0] for point in bbox) + max(point[0] for point in bbox)) / 2

        # Group by approximate Y position (allowing some tolerance)
        row_key = round(center_y / 20) * 20  # Group within 20 pixel tolerance

        if row_key not in rows:
            rows[row_key] = []

        rows[row_key].append((center_x, text.strip()))

    # Sort rows by Y position and columns by X position
    table_data = []
    for row_y in sorted(rows.keys()):
        row_items = sorted(rows[row_y], key=lambda x: x[0])  # Sort by X position
        row_texts = [item[1] for item in row_items]
        if row_texts:  # Only add non-empty rows
            table_data.append(row_texts)

    return table_data if len(table_data) > 1 else None

def extract_tables_with_camelot(image_path):
    """Extract tables from image using Camelot library"""
    if not CAMELOT_AVAILABLE:
        return None

    try:
        print("  🐪 Using Camelot for advanced table extraction...")

        # Convert image to PDF first (Camelot works with PDFs)
        pdf_path = convert_image_to_pdf(image_path)

        if not pdf_path:
            return None

        # Extract tables using Camelot
        tables = camelot.read_pdf(pdf_path, pages='1', flavor='lattice')

        if len(tables) == 0:
            # Try stream flavor if lattice doesn't work
            print("    🔄 Trying stream flavor...")
            tables = camelot.read_pdf(pdf_path, pages='1', flavor='stream')

        if len(tables) > 0:
            print(f"    ✅ Camelot found {len(tables)} table(s)")

            # Get the best table (highest accuracy)
            best_table = max(tables, key=lambda t: getattr(t, 'accuracy', 0))

            # Convert to our format
            table_data = best_table.df.values.tolist()

            # Clean up temporary files
            cleanup_temp_files(pdf_path)

            # Clean the data
            cleaned_data = clean_camelot_data(table_data)

            print(f"    📊 Extracted table: {len(cleaned_data)} rows × {len(cleaned_data[0]) if cleaned_data else 0} columns")
            print(f"    🎯 Table accuracy: {getattr(best_table, 'accuracy', 'N/A')}")

            return cleaned_data
        else:
            print("    ⚠️  No tables found by Camelot")
            cleanup_temp_files(pdf_path)
            return None

    except Exception as e:
        print(f"    ❌ Camelot extraction failed: {e}")
        return None

def convert_image_to_pdf(image_path):
    """Convert image to PDF for Camelot processing"""
    try:
        # Open and convert image
        img = Image.open(image_path)

        # Convert to RGB if necessary
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Save as PDF
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, "temp_table.pdf")
        img.save(pdf_path, "PDF", resolution=300.0, quality=95)

        return pdf_path

    except Exception as e:
        print(f"    ⚠️  Could not convert image to PDF: {e}")
        return None

def cleanup_temp_files(pdf_path):
    """Clean up temporary files"""
    try:
        if os.path.exists(pdf_path):
            os.remove(pdf_path)
            # Also remove the temp directory if empty
            temp_dir = os.path.dirname(pdf_path)
            if os.path.exists(temp_dir) and not os.listdir(temp_dir):
                os.rmdir(temp_dir)
    except Exception as e:
        print(f"    ⚠️  Could not clean up temp files: {e}")

def clean_camelot_data(table_data):
    """Clean and validate table data extracted by Camelot"""
    if not table_data:
        return None

    print("  🧹 Cleaning Camelot-extracted table data...")

    cleaned_data = []

    for row in table_data:
        cleaned_row = []

        for cell in row:
            # Convert to string and clean
            cell_str = str(cell) if cell is not None else ""

            # Remove NaN values
            if cell_str.lower() in ['nan', 'none', '']:
                cell_str = ""

            # Apply basic cleaning
            if cell_str:
                # Fix common OCR errors
                cell_str = cell_str.replace('O', '0')  # O -> 0 in numbers
                cell_str = cell_str.replace('S$', '$').replace('$S', '$')  # Fix currency
                cell_str = ' '.join(cell_str.split())  # Clean whitespace

            cleaned_row.append(cell_str)

        # Only add rows that have some content
        if any(cell.strip() for cell in cleaned_row):
            cleaned_data.append(cleaned_row)

    # Ensure consistent column count
    if cleaned_data:
        max_cols = max(len(row) for row in cleaned_data)
        for row in cleaned_data:
            while len(row) < max_cols:
                row.append("")

    print(f"    ✅ Cleaned table: {len(cleaned_data)} rows × {max_cols if cleaned_data else 0} columns")

    return cleaned_data if cleaned_data else None

def process_image_to_docx(image_path, output_path):
    """Process a single image and convert to Word document"""
    try:
        print(f"🎯 Processing: {os.path.basename(image_path)}")

        img = Image.open(image_path)
        print(f"  📊 Image size: {img.size}")

        # Try Camelot first for best table extraction
        table_data = None
        if CAMELOT_AVAILABLE:
            table_data = extract_tables_with_camelot(image_path)

        # If Camelot didn't work or isn't available, use Smart OCR fallback
        if not table_data or len(table_data) < 2:
            if CAMELOT_AVAILABLE:
                print("  🔍 Camelot extraction unsuccessful, trying Smart OCR fallback...")

            # Preprocess image for better OCR
            img_processed = preprocess_image_for_table(img)

            # Extract text with Smart OCR
            print("  🔍 Running Smart OCR analysis...")
            results = smart_ocr_extraction(img_processed)
            print(f"  📊 Found {len(results)} text regions with smart OCR")

            # Extract table structure
            table_data = extract_table_from_results(results)

        # Create Word document
        doc = Document()
        doc.add_heading(f'Extracted Table: {os.path.basename(image_path)}', level=1)

        # Add processing info
        info_para = doc.add_paragraph()
        info_para.add_run("Processed: ").bold = True
        info_para.add_run(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        info_para.add_run(f"\nText regions found: {len(results)}")

        if table_data and len(table_data) > 1:
            print(f"  ✅ Created table: {len(table_data)} rows")
            create_word_table(doc, table_data)
        else:
            print("  📝 Adding as text paragraphs")
            doc.add_paragraph("Extracted text:")
            for _, text, confidence in results:
                if text.strip() and confidence > 0.3:
                    doc.add_paragraph(f"• {text.strip()}")

        doc.save(output_path)
        print(f"  💾 Saved: {output_path}")
        return True

    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        return False

class YarkTabularExtractionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Yark Tabular Extraction")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        self.root.configure(bg='#f0f0f0')

        # Set icon and logo
        self.setup_icon_and_logo()

        # Variables
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.enable_latex_ocr = tk.BooleanVar(value=LATEX_OCR_AVAILABLE)
        self.enable_image_enhancement = tk.BooleanVar(value=True)
        self.enable_table_detection = tk.BooleanVar(value=True)
        self.processing_active = False
        self.processed_files = 0
        self.total_files = 0

        # Create GUI elements
        self.create_widgets()

        # Initialize with default paths
        self.input_folder.set("C:/Users/<USER>/Downloads/New folder")
        self.output_folder.set("C:/Users/<USER>/Music")
        
    def setup_icon_and_logo(self):
        """Setup application icon and logo"""
        try:
            # Set window icon
            icon_path = os.path.join("logo and icon", "Icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)

            # Load logo for display in GUI only if PIL is available
            if PIL_AVAILABLE:
                logo_path = os.path.join("logo and icon", "logo.png")
                if os.path.exists(logo_path):
                    self.logo_image = Image.open(logo_path)
                    self.logo_image = self.logo_image.resize((64, 64), Image.Resampling.LANCZOS)
                    self.logo_photo = ImageTk.PhotoImage(self.logo_image)
                else:
                    self.logo_photo = None
            else:
                self.logo_photo = None
        except Exception as e:
            print(f"Could not load icon/logo: {e}")
            self.logo_photo = None
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main frame with custom styling
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Header with logo and title
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))

        if self.logo_photo:
            logo_label = ttk.Label(header_frame, image=self.logo_photo)
            logo_label.grid(row=0, column=0, padx=(0, 10))

        title_label = ttk.Label(header_frame, text="Yark Tabular Extraction",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=1, sticky=tk.W)

        subtitle_label = ttk.Label(header_frame, text="Advanced OCR Table Processing with AI Enhancement",
                                  font=('Arial', 9))
        subtitle_label.grid(row=1, column=1, sticky=tk.W)

        # Input Folder Section
        ttk.Label(main_frame, text="Input Folder:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=8)
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=8)
        input_frame.columnconfigure(0, weight=1)

        self.input_entry = ttk.Entry(input_frame, textvariable=self.input_folder, width=60, font=('Arial', 9))
        self.input_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))
        ttk.Button(input_frame, text="Browse", command=self.browse_input_folder, width=10).grid(row=0, column=1)

        # Output Folder Section
        ttk.Label(main_frame, text="Output Folder:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=8)
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=8)
        output_frame.columnconfigure(0, weight=1)

        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_folder, width=60, font=('Arial', 9))
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))
        ttk.Button(output_frame, text="Browse", command=self.browse_output_folder, width=10).grid(row=0, column=1)
        
        # Configuration Section
        config_frame = ttk.LabelFrame(main_frame, text="Configuration", padding="12")
        config_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)

        # Create checkboxes with better styling
        self.latex_check = ttk.Checkbutton(config_frame, text="✓ Enable LaTeX OCR for mathematical content",
                                          variable=self.enable_latex_ocr)
        self.latex_check.grid(row=0, column=0, sticky=tk.W, pady=4)

        self.enhance_check = ttk.Checkbutton(config_frame, text="✓ Apply image enhancement preprocessing",
                                            variable=self.enable_image_enhancement)
        self.enhance_check.grid(row=1, column=0, sticky=tk.W, pady=4)

        self.table_check = ttk.Checkbutton(config_frame, text="✓ Detect and structure table content",
                                          variable=self.enable_table_detection)
        self.table_check.grid(row=2, column=0, sticky=tk.W, pady=4)

        # Progress Section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="12")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)
        progress_frame.columnconfigure(0, weight=1)

        self.progress_label = ttk.Label(progress_frame, text="Processing completed!", font=('Arial', 10))
        self.progress_label.grid(row=0, column=0, sticky=tk.W, pady=4)

        # Progress bar with determinate mode
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate', length=400)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=8)
        self.progress_bar['value'] = 100  # Start at 100% to show "completed" state
        
        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=20)

        # Style buttons to match the design
        self.start_button = ttk.Button(button_frame, text="Start Processing",
                                      command=self.start_processing, width=15)
        self.start_button.grid(row=0, column=0, padx=8)

        self.view_logs_button = ttk.Button(button_frame, text="View Logs",
                                          command=self.view_logs, width=12)
        self.view_logs_button.grid(row=0, column=1, padx=8)

        self.exit_button = ttk.Button(button_frame, text="Exit",
                                     command=self.root.quit, width=10)
        self.exit_button.grid(row=0, column=2, padx=8)
        
        # Processing Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Processing Log", padding="12")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=15)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)

        # Create log text area with better styling
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=85,
                                                 font=('Consolas', 9), wrap=tk.WORD,
                                                 bg='#f8f8f8', fg='#333333')
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Add enhanced system status
        self.log_message("🚀 Yark Tabular Extraction initialized successfully")

        if MULTI_OCR_AVAILABLE:
            self.log_message("✅ Multi-Engine OCR System ready")
            try:
                # Get engine status
                engine_status = multi_ocr.get_engine_status()
                available_engines = engine_status['available_engines']
                self.log_message(f"🎯 Available OCR engines: {', '.join(available_engines)}")

                # Show specific engine capabilities
                if 'easyocr' in available_engines:
                    self.log_message("  📝 EasyOCR: General text recognition")
                if 'tesseract' in available_engines:
                    self.log_message("  🔤 Tesseract: Fast text extraction")
                if 'paddleocr' in available_engines:
                    self.log_message("  🏛️ PaddleOCR: Advanced structured data")
                if 'latex' in available_engines:
                    self.log_message("  🧮 LaTeX OCR: Mathematical expressions")

            except Exception as e:
                self.log_message(f"⚠️ Error getting engine status: {e}")

        elif OCR_AVAILABLE:
            self.log_message("✅ Basic EasyOCR ready (fallback mode)")
            if LATEX_OCR_AVAILABLE:
                self.log_message("✅ LaTeX OCR available for mathematical content")
            else:
                self.log_message("⚠️ LaTeX OCR not available. Install with: pip install pix2tex")
        else:
            self.log_message("❌ OCR functions not available. Please check dependencies")

        # Show Camelot status
        if ENHANCED_CAMELOT_AVAILABLE:
            self.log_message("✅ Enhanced Camelot PDF table extraction ready")
            self.log_message("  🐪 Advanced multi-method table extraction")
            self.log_message("  📊 Automatic quality assessment and optimization")
        elif CAMELOT_AVAILABLE:
            self.log_message("✅ Basic Camelot PDF table extraction available")
        else:
            self.log_message("⚠️ Camelot PDF extraction not available")
            self.log_message("  💡 Install with: pip install camelot-py[cv]")

        self.log_message("📁 Ready to process table images")
        self.log_message("💡 Select input and output folders, then click 'Start Processing'")
    
    def browse_input_folder(self):
        """Browse for input folder"""
        folder = filedialog.askdirectory(title="Select Input Folder with Table Images")
        if folder:
            self.input_folder.set(folder)
            self.log_message(f"📁 Input folder selected: {folder}")
    
    def browse_output_folder(self):
        """Browse for output folder"""
        folder = filedialog.askdirectory(title="Select Output Folder for Word Files")
        if folder:
            self.output_folder.set(folder)
            self.log_message(f"📁 Output folder selected: {folder}")
    
    def log_message(self, message):
        """Add message to log with timestamp"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_processing(self):
        """Start the OCR processing in a separate thread"""
        if self.processing_active:
            messagebox.showwarning("Processing Active", "Processing is already in progress!")
            return

        # Check if OCR is available
        if not OCR_AVAILABLE:
            messagebox.showerror("OCR Not Available", "EasyOCR is not available. Please install dependencies:\npip install easyocr opencv-python pillow python-docx numpy")
            return

        # Validate inputs
        if not self.input_folder.get():
            messagebox.showerror("Error", "Please select an input folder")
            return

        if not self.output_folder.get():
            messagebox.showerror("Error", "Please select an output folder")
            return

        if not os.path.exists(self.input_folder.get()):
            messagebox.showerror("Error", "Input folder does not exist")
            return

        if not os.path.exists(self.output_folder.get()):
            messagebox.showerror("Error", "Output folder does not exist")
            return

        # Clear previous log entries for new processing
        self.log_text.delete(1.0, tk.END)

        # Start processing in separate thread
        self.processing_active = True
        self.start_button.config(state='disabled')
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start()
        self.progress_label.config(text="Processing...")

        processing_thread = threading.Thread(target=self.process_images)
        processing_thread.daemon = True
        processing_thread.start()
    
    def process_images(self):
        """Process all images in the input folder"""
        try:
            input_dir = self.input_folder.get()
            output_dir = self.output_folder.get()

            self.log_message("🎯 Starting OCR Pipeline...")

            # Find supported image files
            supported_ext = ('.png', '.jpg', '.jpeg', '.tif', '.bmp')
            files = [f for f in os.listdir(input_dir) if f.lower().endswith(supported_ext)]

            if not files:
                self.log_message("❌ No supported image files found in input folder")
                self.root.after(0, lambda: messagebox.showinfo("No Images", "No supported image files found in selected folder."))
                return

            self.total_files = len(files)
            self.processed_files = 0
            self.log_message(f"📊 Found {len(files)} image files to process")

            # Switch to determinate progress bar
            self.root.after(0, lambda: self.progress_bar.config(mode='determinate', maximum=len(files), value=0))

            # Process each file
            success_count = 0
            for i, file in enumerate(files, 1):
                self.log_message(f"Processing: {file}")
                self.root.after(0, lambda f=file, idx=i: self.progress_label.config(text=f"Processing {idx}/{len(files)}: {f}"))

                img_path = os.path.join(input_dir, file)
                output_path = os.path.join(output_dir, os.path.splitext(file)[0] + ".docx")

                try:
                    if process_image_to_docx(img_path, output_path):
                        success_count += 1
                        self.log_message(f"✅ Successfully processed {file}")
                    else:
                        self.log_message(f"❌ Failed to process {file}")
                except Exception as e:
                    self.log_message(f"❌ Error processing {file}: {str(e)}")

                # Update progress bar
                self.processed_files = i
                self.root.after(0, lambda: self.progress_bar.config(value=self.processed_files))

            # Show completion message
            self.log_message(f"🎉 Pipeline processing completed!")
            self.log_message(f"📊 Successfully processed {success_count}/{len(files)} files")

            # Show completion dialog
            self.root.after(0, lambda: messagebox.showinfo("Processing Complete",
                f"Successfully processed {success_count} out of {len(files)} files.\n\nOutput files saved to:\n{output_dir}"))

        except Exception as e:
            error_msg = f"❌ Processing error: {str(e)}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("Processing Error", str(e)))

        finally:
            # Reset UI state
            self.processing_active = False
            self.root.after(0, self.reset_ui_after_processing)
    
    def reset_ui_after_processing(self):
        """Reset UI state after processing completes"""
        self.progress_bar.stop()
        self.progress_bar.config(value=100)  # Show completed state
        self.progress_label.config(text="Processing completed!")
        self.start_button.config(state='normal')
    
    def view_logs(self):
        """Show logs in a separate window"""
        log_window = tk.Toplevel(self.root)
        log_window.title("Processing Logs - Yark Tabular Extraction")
        log_window.geometry("800x600")
        
        # Create scrolled text widget
        log_display = scrolledtext.ScrolledText(log_window, wrap=tk.WORD)
        log_display.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Copy current log content
        log_content = self.log_text.get(1.0, tk.END)
        log_display.insert(1.0, log_content)
        log_display.config(state='disabled')

def main():
    """Main function to run the GUI application"""
    root = tk.Tk()
    YarkTabularExtractionGUI(root)
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
