"""
Multi-Engine OCR System for Yark Tabular Extraction
Provides intelligent OCR engine selection and fallback mechanisms
"""

import os
import numpy as np
import cv2
from PIL import Image
import re
import time
from typing import List, Tuple, Dict, Optional, Any

# OCR Engine Imports with availability checking
OCR_ENGINES = {}

# EasyOCR
try:
    import easyocr
    OCR_ENGINES['easyocr'] = True
    print("✅ EasyOCR available")
except ImportError:
    OCR_ENGINES['easyocr'] = False
    print("⚠️  EasyOCR not available")

# Tesseract
try:
    import pytesseract
    OCR_ENGINES['tesseract'] = True
    print("✅ Tesseract available")
except ImportError:
    OCR_ENGINES['tesseract'] = False
    print("⚠️  Tesseract not available")

# PaddleOCR
try:
    from paddleocr import PaddleOCR
    OCR_ENGINES['paddleocr'] = True
    print("✅ PaddleOCR available")
except ImportError:
    OCR_ENGINES['paddleocr'] = False
    print("⚠️  PaddleOCR not available")

# LaTeX OCR
try:
    from pix2tex.cli import LatexOCR
    OCR_ENGINES['latex'] = True
    print("✅ LaTeX OCR available")
except ImportError:
    OCR_ENGINES['latex'] = False
    print("⚠️  LaTeX OCR not available")

class MultiEngineOCR:
    """Advanced multi-engine OCR system with intelligent engine selection"""
    
    def __init__(self):
        self.engines = {}
        self.engine_performance = {}
        self.initialize_engines()
        
    def initialize_engines(self):
        """Initialize available OCR engines"""
        print("🚀 Initializing Multi-Engine OCR System...")
        
        # Initialize EasyOCR
        if OCR_ENGINES['easyocr']:
            try:
                self.engines['easyocr'] = easyocr.Reader(['en'])
                self.engine_performance['easyocr'] = {'success_rate': 0.85, 'speed': 0.7, 'accuracy': 0.8}
                print("  ✅ EasyOCR initialized")
            except Exception as e:
                print(f"  ❌ EasyOCR initialization failed: {e}")
                OCR_ENGINES['easyocr'] = False
        
        # Initialize Tesseract
        if OCR_ENGINES['tesseract']:
            try:
                # Test Tesseract availability with better error handling
                import subprocess
                import shutil

                # Check if tesseract executable is available
                tesseract_cmd = shutil.which('tesseract')
                if tesseract_cmd is None:
                    # Try common installation paths
                    common_paths = [
                        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                        '/usr/bin/tesseract',
                        '/usr/local/bin/tesseract'
                    ]

                    for path in common_paths:
                        if os.path.exists(path):
                            pytesseract.pytesseract.tesseract_cmd = path
                            tesseract_cmd = path
                            break

                if tesseract_cmd:
                    # Test with a simple version check
                    result = subprocess.run([tesseract_cmd, '--version'],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.engines['tesseract'] = pytesseract
                        self.engine_performance['tesseract'] = {'success_rate': 0.75, 'speed': 0.9, 'accuracy': 0.7}
                        print("  ✅ Tesseract initialized")
                    else:
                        raise Exception("Tesseract executable test failed")
                else:
                    raise Exception("Tesseract executable not found in PATH or common locations")

            except Exception as e:
                print(f"  ❌ Tesseract initialization failed: {e}")
                print("  💡 Install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki")
                print("  💡 Or install via: winget install UB-Mannheim.TesseractOCR")
                OCR_ENGINES['tesseract'] = False
        
        # Initialize PaddleOCR
        if OCR_ENGINES['paddleocr']:
            try:
                # Initialize PaddleOCR with compatible parameters
                # Note: show_log parameter may not be available in all versions
                try:
                    # Try with show_log parameter first
                    self.engines['paddleocr'] = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
                except TypeError:
                    # Fallback without show_log parameter
                    print("  ⚠️  PaddleOCR show_log parameter not supported, using default logging")
                    self.engines['paddleocr'] = PaddleOCR(use_angle_cls=True, lang='en')

                self.engine_performance['paddleocr'] = {'success_rate': 0.9, 'speed': 0.6, 'accuracy': 0.85}
                print("  ✅ PaddleOCR initialized")
            except Exception as e:
                print(f"  ❌ PaddleOCR initialization failed: {e}")
                print("  💡 Try: pip install paddlepaddle paddleocr")
                OCR_ENGINES['paddleocr'] = False
        
        # Initialize LaTeX OCR
        if OCR_ENGINES['latex']:
            try:
                self.engines['latex'] = LatexOCR()
                self.engine_performance['latex'] = {'success_rate': 0.95, 'speed': 0.4, 'accuracy': 0.95}
                print("  ✅ LaTeX OCR initialized")
            except Exception as e:
                print(f"  ❌ LaTeX OCR initialization failed: {e}")
                OCR_ENGINES['latex'] = False
        
        available_engines = len(self.engines)
        print(f"🎯 Multi-Engine OCR initialized with {available_engines} engines")
    
    def select_best_engine(self, image: Image.Image, content_type: str = "general") -> str:
        """Select the best OCR engine based on content type and image characteristics"""
        
        # Content-based engine selection
        if content_type == "mathematical" and 'latex' in self.engines:
            return 'latex'
        elif content_type == "financial" and 'paddleocr' in self.engines:
            return 'paddleocr'  # Good for structured data
        elif content_type == "handwritten" and 'easyocr' in self.engines:
            return 'easyocr'  # Better for handwritten text
        
        # Image quality-based selection
        image_quality = self.assess_image_quality(image)
        
        if image_quality['is_high_quality']:
            # High quality images - use most accurate engine
            if 'paddleocr' in self.engines:
                return 'paddleocr'
            elif 'easyocr' in self.engines:
                return 'easyocr'
        else:
            # Lower quality images - use more robust engine
            if 'easyocr' in self.engines:
                return 'easyocr'
            elif 'tesseract' in self.engines:
                return 'tesseract'
        
        # Fallback to first available engine
        return list(self.engines.keys())[0] if self.engines else None
    
    def assess_image_quality(self, image: Image.Image) -> Dict[str, Any]:
        """Assess image quality to help with engine selection"""
        img_array = np.array(image.convert('L'))  # Convert to grayscale
        
        # Calculate image metrics
        height, width = img_array.shape
        
        # Sharpness (Laplacian variance)
        laplacian_var = cv2.Laplacian(img_array, cv2.CV_64F).var()
        
        # Contrast (standard deviation)
        contrast = np.std(img_array)
        
        # Resolution
        total_pixels = height * width
        
        # Noise level (estimate using high-frequency content)
        blur_kernel = np.ones((5,5), np.float32) / 25
        blurred = cv2.filter2D(img_array, -1, blur_kernel)
        noise_level = np.mean(np.abs(img_array.astype(float) - blurred.astype(float)))
        
        is_high_quality = (
            laplacian_var > 100 and  # Sharp enough
            contrast > 30 and        # Good contrast
            total_pixels > 10000 and # Sufficient resolution
            noise_level < 15         # Low noise
        )
        
        return {
            'is_high_quality': is_high_quality,
            'sharpness': laplacian_var,
            'contrast': contrast,
            'resolution': total_pixels,
            'noise_level': noise_level
        }
    
    def extract_text_with_engine(self, image: Image.Image, engine_name: str) -> Tuple[str, float]:
        """Extract text using a specific OCR engine"""
        if engine_name not in self.engines:
            return "", 0.0
        
        try:
            start_time = time.time()
            
            if engine_name == 'easyocr':
                img_array = np.array(image)
                results = self.engines['easyocr'].readtext(img_array)
                if results:
                    # Get best result
                    best_result = max(results, key=lambda x: x[2])
                    text = best_result[1]
                    confidence = best_result[2]
                else:
                    text, confidence = "", 0.0
            
            elif engine_name == 'tesseract':
                # Configure Tesseract for better table recognition
                config = '--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,()[]{}+-=/$%'
                text = pytesseract.image_to_string(image, config=config).strip()
                # Tesseract doesn't provide confidence directly, estimate it
                confidence = 0.8 if text else 0.0
            
            elif engine_name == 'paddleocr':
                img_array = np.array(image)
                results = self.engines['paddleocr'].ocr(img_array, cls=True)
                if results and results[0]:
                    # Combine all detected text
                    texts = []
                    confidences = []
                    for line in results[0]:
                        if len(line) >= 2:
                            texts.append(line[1][0])
                            confidences.append(line[1][1])
                    
                    text = ' '.join(texts) if texts else ""
                    confidence = sum(confidences) / len(confidences) if confidences else 0.0
                else:
                    text, confidence = "", 0.0
            
            elif engine_name == 'latex':
                text = self.engines['latex'](image)
                confidence = 0.9 if text and text.strip() else 0.0
            
            else:
                text, confidence = "", 0.0
            
            processing_time = time.time() - start_time
            
            # Update engine performance metrics
            if engine_name in self.engine_performance:
                perf = self.engine_performance[engine_name]
                # Simple moving average for success rate
                success = 1.0 if confidence > 0.3 else 0.0
                perf['success_rate'] = 0.9 * perf['success_rate'] + 0.1 * success
                # Update speed metric (inverse of processing time)
                speed_score = min(1.0, 1.0 / max(0.1, processing_time))
                perf['speed'] = 0.9 * perf['speed'] + 0.1 * speed_score
            
            return text, confidence
            
        except Exception as e:
            print(f"    ❌ {engine_name} OCR failed: {e}")
            return "", 0.0
    
    def extract_text_multi_engine(self, image: Image.Image, content_type: str = "general") -> Dict[str, Any]:
        """Extract text using multiple engines with intelligent selection and fallback"""
        
        # Select primary engine
        primary_engine = self.select_best_engine(image, content_type)
        
        if not primary_engine:
            return {"text": "", "confidence": 0.0, "engine": "none", "fallback_used": False}
        
        print(f"    🎯 Primary engine selected: {primary_engine}")
        
        # Try primary engine
        text, confidence = self.extract_text_with_engine(image, primary_engine)
        
        # If primary engine fails or has low confidence, try fallback engines
        fallback_used = False
        if confidence < 0.4 and len(self.engines) > 1:
            print(f"    🔄 Primary engine confidence low ({confidence:.2f}), trying fallback engines...")
            
            # Try other engines in order of performance
            fallback_engines = sorted(
                [e for e in self.engines.keys() if e != primary_engine],
                key=lambda e: self.engine_performance.get(e, {}).get('success_rate', 0),
                reverse=True
            )
            
            for fallback_engine in fallback_engines:
                fallback_text, fallback_confidence = self.extract_text_with_engine(image, fallback_engine)
                
                if fallback_confidence > confidence:
                    print(f"    ✅ Fallback engine {fallback_engine} performed better ({fallback_confidence:.2f} vs {confidence:.2f})")
                    text = fallback_text
                    confidence = fallback_confidence
                    primary_engine = fallback_engine
                    fallback_used = True
                    break
        
        return {
            "text": text,
            "confidence": confidence,
            "engine": primary_engine,
            "fallback_used": fallback_used
        }
    
    def detect_content_type(self, image: Image.Image) -> str:
        """Advanced content type detection with pattern analysis and visual features"""

        # Multi-stage content analysis
        content_scores = {
            'mathematical': 0.0,
            'financial': 0.0,
            'handwritten': 0.0,
            'technical': 0.0,
            'general': 0.0
        }

        # Stage 1: Quick text analysis
        quick_engine = 'tesseract' if 'tesseract' in self.engines else list(self.engines.keys())[0]
        quick_text, confidence = self.extract_text_with_engine(image, quick_engine)

        if quick_text:
            content_scores.update(self.analyze_text_patterns(quick_text, confidence))

        # Stage 2: Visual feature analysis
        visual_scores = self.analyze_visual_features(image)

        # Combine scores with weights
        for content_type in content_scores:
            if content_type in visual_scores:
                content_scores[content_type] = (
                    content_scores[content_type] * 0.7 +  # Text analysis weight
                    visual_scores[content_type] * 0.3     # Visual analysis weight
                )

        # Return the content type with highest score
        best_type = max(content_scores, key=content_scores.get)
        best_score = content_scores[best_type]

        # Only return specific type if confidence is high enough
        if best_score > 0.3:
            print(f"    🎯 Content type detected: {best_type} (confidence: {best_score:.2f})")
            return best_type
        else:
            print(f"    🎯 Content type: general (low confidence: {best_score:.2f})")
            return "general"

    def analyze_text_patterns(self, text: str, confidence: float) -> Dict[str, float]:
        """Analyze text patterns to determine content type"""
        scores = {
            'mathematical': 0.0,
            'financial': 0.0,
            'handwritten': 0.0,
            'technical': 0.0,
            'general': 0.1
        }

        if not text:
            return scores

        # Mathematical content analysis
        math_patterns = [
            (r'[=+\-×÷∑∏∫]', 0.3),  # Mathematical operators
            (r'\b(equation|formula|ratio|solution|sqrt|sin|cos|tan)\b', 0.4),
            (r'\b\d+\s*[×÷]\s*\d+', 0.5),  # Multiplication/division
            (r'\b\d+/\d+\b', 0.3),  # Fractions
            (r'\^\d+|\b\d+\^\d+', 0.4),  # Exponents
            (r'[()]\s*\d+', 0.2),  # Numbers in parentheses
        ]

        for pattern, weight in math_patterns:
            if re.search(pattern, text):
                scores['mathematical'] += weight

        # Financial content analysis
        financial_patterns = [
            (r'[\$£€¥₹]\s*[\d,]+', 0.5),  # Currency amounts
            (r'\b(assets|liabilities|equity|capital|revenue|profit|loss|balance|sheet)\b', 0.4),
            (r'\b(debit|credit|account|ledger|journal|trial)\b', 0.3),
            (r'\b(cash|inventory|receivables|payables|depreciation)\b', 0.3),
            (r'\(\s*[\d,]+\s*\)', 0.3),  # Negative amounts in parentheses
            (r'\b\d{1,3}(,\d{3})*\.\d{2}\b', 0.2),  # Formatted currency amounts
        ]

        for pattern, weight in financial_patterns:
            if re.search(pattern, text):
                scores['financial'] += weight

        # Technical content analysis
        technical_patterns = [
            (r'\b(specification|parameter|configuration|protocol)\b', 0.3),
            (r'\b[A-Z]{2,}\d+\b', 0.2),  # Technical codes
            (r'\b\d+\.\d+\.\d+', 0.2),  # Version numbers
            (r'\b(API|HTTP|TCP|UDP|SQL|XML|JSON)\b', 0.4),
        ]

        for pattern, weight in technical_patterns:
            if re.search(pattern, text):
                scores['technical'] += weight

        # Handwritten characteristics
        if confidence < 0.6:  # Low OCR confidence might indicate handwriting
            scores['handwritten'] += 0.2

        # Check for irregular spacing/formatting
        words = text.split()
        if len(words) > 3:
            short_words = sum(1 for word in words if len(word) <= 2)
            if short_words / len(words) > 0.4:
                scores['handwritten'] += 0.3

        # Normalize scores
        max_score = max(scores.values())
        if max_score > 0:
            for key in scores:
                scores[key] = min(1.0, scores[key] / max_score)

        return scores

    def analyze_visual_features(self, image: Image.Image) -> Dict[str, float]:
        """Analyze visual features of the image for content type detection"""
        scores = {
            'mathematical': 0.0,
            'financial': 0.0,
            'handwritten': 0.0,
            'technical': 0.0,
            'general': 0.1
        }

        try:
            import cv2
            import numpy as np

            # Convert to grayscale for analysis
            img_array = np.array(image.convert('L'))

            # Detect lines (tables often have many lines)
            edges = cv2.Canny(img_array, 50, 150)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, minLineLength=30, maxLineGap=10)

            if lines is not None:
                line_count = len(lines)
                if line_count > 20:  # Many lines suggest structured data
                    scores['financial'] += 0.3
                    scores['technical'] += 0.2
                elif line_count > 10:
                    scores['financial'] += 0.2

            # Analyze text density and distribution
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # Calculate contour characteristics
                areas = [cv2.contourArea(c) for c in contours if cv2.contourArea(c) > 10]

                if areas:
                    area_variance = np.var(areas)
                    mean_area = np.mean(areas)

                    # High variance might indicate mixed content (math formulas)
                    if area_variance > mean_area * 2:
                        scores['mathematical'] += 0.2

                    # Many small contours might indicate handwriting
                    small_contours = sum(1 for area in areas if area < 100)
                    if small_contours / len(areas) > 0.7:
                        scores['handwritten'] += 0.3

        except Exception:
            # If visual analysis fails, don't affect the scoring
            pass

        return scores
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get status and performance metrics of all OCR engines"""
        status = {
            'available_engines': list(self.engines.keys()),
            'total_engines': len(self.engines),
            'performance_metrics': self.engine_performance.copy()
        }
        return status

# Global instance
multi_ocr = MultiEngineOCR()
